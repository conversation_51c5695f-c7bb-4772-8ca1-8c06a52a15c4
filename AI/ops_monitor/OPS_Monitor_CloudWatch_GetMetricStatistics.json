{"name": "OPS-Monitor-CloudWatch-GetMetricStatistics", "nodes": [{"parameters": {"functionCode": "// Process metric statistics data\nconst metricData = $input.item.json;\n\n// Format the output for easier processing\nreturn {\n  json: {\n    metricName: metricData.Label,\n    namespace: metricData.Namespace || 'AWS/EC2',\n    datapoints: metricData.Datapoints || [],\n    // Add timestamp for reference\n    retrievedAt: new Date().toISOString(),\n    // Calculate basic statistics if datapoints exist\n    summary: metricData.Datapoints ? {\n      totalDatapoints: metricData.Datapoints.length,\n      latestValue: metricData.Datapoints.length > 0 ? metricData.Datapoints[metricData.Datapoints.length - 1] : null,\n      averageValue: metricData.Datapoints.length > 0 ? \n        metricData.Datapoints.reduce((sum, dp) => sum + (dp.Average || 0), 0) / metricData.Datapoints.length : 0\n    } : null\n  }\n};"}, "id": "13a54d2e-1b3d-4ae1-ad34-121b1ca1c6a1", "name": "Format Metric Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [820, -220], "notes": "Processes metric statistics and formats for easier use"}, {"parameters": {"region": "ap-southeast-1", "service": "CloudWatch", "operation": "GetMetricStatistics", "requestHasInput": true, "requestInput": "{\n  \"Namespace\": \"AWS/EC2\",\n  \"MetricName\": \"MemoryUtilization\",\n  \"Dimensions\": [\n    {\n      \"Name\": \"InstanceId\",\n      \"Value\": \"i-1234567890abcdef0\"\n    }\n  ],\n  \"StartTime\": \"2024-01-01T00:00:00Z\",\n  \"EndTime\": \"2024-01-01T23:59:59Z\",\n  \"Period\": 300,\n  \"Statistics\": [\"Average\", \"Maximum\", \"Minimum\"]\n}", "version": "2010-08-01"}, "type": "n8n-nodes-aws-sdk-v3.AWSSDKWrapper", "typeVersion": 1, "position": [640, -220], "id": "f4787283-4ec8-4a1e-84e8-9a4b72a8aa08", "name": "AWS Service Request - GetMetricStatistics", "credentials": {"awsSdkWrapperCredentialsApi": {"id": "XCHImWEP1FWyMVeg", "name": "AWS SDK Wrapper Credentials account"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [440, -220], "id": "641715bf-c062-40f3-a33f-0022b261d7dc", "name": "When clicking 'Execute workflow'"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "AWS Service Request - GetMetricStatistics", "type": "main", "index": 0}]]}, "AWS Service Request - GetMetricStatistics": {"main": [[{"node": "Format Metric Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72713bae-0c77-44f9-a5a0-79f0117fdaff", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "2IyyBQV1nzQdVNAr", "tags": []}
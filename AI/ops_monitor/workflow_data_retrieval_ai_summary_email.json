{"name": "OPS-Monitor", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-760, 240], "id": "ba4fa39f-a4e3-41ec-807c-f761152280b8", "name": "When clicking 'Execute workflow'"}, {"parameters": {"functionCode": "// Extract and format the CloudWatch data for AI processing\n// The data comes directly from the Execute Workflow node\nconst dashboardData = $input.item.json;\n\n// Extract basic information\nconst dashboardName = dashboardData.dashboardName || 'Unknown Dashboard';\nconst retrievedAt = dashboardData.retrievedAt || new Date().toISOString();\nconst timeRange = dashboardData.timeRange || {};\nconst totalMetrics = dashboardData.totalMetrics || 0;\nconst metrics = dashboardData.metrics || [];\nconst summary = dashboardData.summary || {};\nconst messages = dashboardData.messages || [];\n\n// Calculate overall statistics from all metrics\nlet overallStats = {\n  totalServices: metrics.length,\n  servicesWithData: metrics.filter(m => m.statusCode === 'Complete').length,\n  averageMemoryUsage: 0,\n  highestMemoryUsage: 0,\n  lowestMemoryUsage: 100,\n  servicesAbove50Percent: [],\n  servicesAbove70Percent: [],\n  stableServices: [],\n  variableServices: []\n};\n\n// Analyze each metric\nmetrics.forEach(metric => {\n  if (metric.statistics && metric.statusCode === 'Complete') {\n    const current = metric.statistics.current;\n    const average = metric.statistics.average;\n    const max = metric.statistics.maximum;\n    const min = metric.statistics.minimum;\n    \n    // Update overall stats\n    overallStats.averageMemoryUsage += current;\n    if (current > overallStats.highestMemoryUsage) {\n      overallStats.highestMemoryUsage = current;\n    }\n    if (current < overallStats.lowestMemoryUsage) {\n      overallStats.lowestMemoryUsage = current;\n    }\n    \n    // Categorize services by memory usage\n    const serviceName = metric.label.split('-Service-')[0] || metric.label;\n    if (current > 70) {\n      overallStats.servicesAbove70Percent.push(`${serviceName} (${current.toFixed(1)}%)`);\n    } else if (current > 50) {\n      overallStats.servicesAbove50Percent.push(`${serviceName} (${current.toFixed(1)}%)`);\n    }\n    \n    // Check stability (difference between max and min)\n    const variation = max - min;\n    if (variation < 1) {\n      overallStats.stableServices.push(`${serviceName} (±${variation.toFixed(2)}%)`);\n    } else if (variation > 5) {\n      overallStats.variableServices.push(`${serviceName} (±${variation.toFixed(2)}%)`);\n    }\n  }\n});\n\n// Calculate average\nif (overallStats.servicesWithData > 0) {\n  overallStats.averageMemoryUsage = overallStats.averageMemoryUsage / overallStats.servicesWithData;\n}\n\n// Determine overall health status\nlet overallStatus = 'Healthy';\nif (overallStats.servicesAbove70Percent.length > 0) {\n  overallStatus = 'Critical';\n} else if (overallStats.servicesAbove50Percent.length > 3) {\n  overallStatus = 'Warning';\n} else if (overallStats.variableServices.length > 2) {\n  overallStatus = 'Unstable';\n}\n\n// Create detailed text summary for AI processing\nconst textSummary = `\nCloudWatch Dashboard Monitoring Report\n=====================================\n\nDashboard: ${dashboardName}\nRetrieved At: ${retrievedAt}\nTime Range: ${timeRange.start || 'N/A'} to ${timeRange.end || 'N/A'}\nTotal Metrics Collected: ${totalMetrics}\nTotal Data Points: ${summary.totalDataPoints || 0}\nOverall Status: ${overallStatus}\n\nOVERALL STATISTICS:\n- Total Services Monitored: ${overallStats.totalServices}\n- Services with Complete Data: ${overallStats.servicesWithData}\n- Average Memory Usage: ${overallStats.averageMemoryUsage.toFixed(2)}%\n- Highest Memory Usage: ${overallStats.highestMemoryUsage.toFixed(2)}%\n- Lowest Memory Usage: ${overallStats.lowestMemoryUsage.toFixed(2)}%\n\nSERVICES REQUIRING ATTENTION:\n${overallStats.servicesAbove70Percent.length > 0 ? \n  'Critical (>70% Memory):\\n' + overallStats.servicesAbove70Percent.map(s => '  - ' + s).join('\\n') : \n  'No services above 70% memory usage'}\n\n${overallStats.servicesAbove50Percent.length > 0 ? \n  'Warning (50-70% Memory):\\n' + overallStats.servicesAbove50Percent.map(s => '  - ' + s).join('\\n') : \n  'No services in warning range'}\n\nSERVICE STABILITY:\n${overallStats.stableServices.length > 0 ? \n  'Stable Services (low variation):\\n' + overallStats.stableServices.map(s => '  - ' + s).join('\\n') : \n  'No highly stable services detected'}\n\n${overallStats.variableServices.length > 0 ? \n  'Variable Services (high variation):\\n' + overallStats.variableServices.map(s => '  - ' + s).join('\\n') : \n  'No highly variable services detected'}\n\nTOP 5 HIGHEST MEMORY USAGE SERVICES:\n${metrics\n  .filter(m => m.statusCode === 'Complete')\n  .sort((a, b) => b.statistics.current - a.statistics.current)\n  .slice(0, 5)\n  .map(m => `  - ${m.label.split('-Service-')[0]}: ${m.statistics.current.toFixed(2)}% (avg: ${m.statistics.average.toFixed(2)}%)`)\n  .join('\\n')}\n\nMESSAGES/ALERTS:\n${messages.length > 0 ? messages.join('\\n') : 'No alerts or messages'}\n`;\n\n// Format the data for further processing\nconst formattedData = {\n  dashboardName: dashboardName,\n  retrievedAt: retrievedAt,\n  timeRange: timeRange,\n  totalMetrics: totalMetrics,\n  overallStats: overallStats,\n  overallStatus: overallStatus,\n  metrics: metrics,\n  summary: summary,\n  messages: messages\n};\n\nreturn {\n  json: {\n    formattedData: formattedData,\n    textSummary: textSummary,\n    rawDashboardData: dashboardData\n  }\n};"}, "id": "0de3b835-9638-4186-aafd-79231f4e0d47", "name": "Format Data for AI", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-340, 240], "notes": "Extracts and formats CloudWatch data for AI summarization"}, {"parameters": {"functionCode": "// Format the AI summary for email\n// Handle Gemini API response format\nlet aiSummary = 'No summary available';\n\n// Extract text from Gemini HTTP API response (prioritize this format)\nif ($input.item.json.candidates && $input.item.json.candidates.length > 0) {\n  // Direct Gemini API response\n  const candidate = $input.item.json.candidates[0];\n  if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {\n    aiSummary = candidate.content.parts[0].text || 'No summary available';\n  }\n} else if ($input.item.json.text) {\n  // Fallback for other AI services\n  aiSummary = $input.item.json.text;\n} else if ($input.item.json.message?.content) {\n  // Fallback for OpenAI format\n  aiSummary = $input.item.json.message.content;\n}\n\nconst originalData = $('Format Data for AI').item.json.formattedData;\nconst overallStats = originalData.overallStats || {};\n\n// Create email subject with status indicator\nconst currentDate = new Date().toLocaleDateString();\nconst currentTime = new Date().toLocaleTimeString();\nconst statusEmoji = originalData.overallStatus === 'Critical' ? '🚨' : \n                   originalData.overallStatus === 'Warning' ? '⚠️' : \n                   originalData.overallStatus === 'Unstable' ? '📊' : '✅';\nconst subject = `${statusEmoji} CRM Monitoring Report - ${originalData.dashboardName} - ${currentDate}`;\n\n// Determine status color class\nconst getStatusClass = (status) => {\n  switch(status?.toLowerCase()) {\n    case 'critical': return 'status-error';\n    case 'warning': return 'status-warning';\n    case 'unstable': return 'status-warning';\n    default: return 'status-ok';\n  }\n};\n\n// Create email body with enhanced HTML formatting\nconst emailBody = `\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }\n    .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }\n    .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .metrics { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }\n    .stat-box { background: white; padding: 10px; border-radius: 3px; border-left: 4px solid #007bff; }\n    .footer { font-size: 12px; color: #666; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }\n    .status-ok { color: #28a745; font-weight: bold; }\n    .status-warning { color: #ffc107; font-weight: bold; }\n    .status-error { color: #dc3545; font-weight: bold; }\n    .service-list { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }\n    .critical-services { border-left: 4px solid #dc3545; }\n    .warning-services { border-left: 4px solid #ffc107; }\n    .stable-services { border-left: 4px solid #28a745; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h2>${statusEmoji} CloudWatch CRM Production Monitoring Report</h2>\n    <p><strong>Dashboard:</strong> ${originalData.dashboardName}</p>\n    <p><strong>Generated:</strong> ${currentDate} at ${currentTime}</p>\n    <p><strong>Retrieved At:</strong> ${new Date(originalData.retrievedAt).toLocaleString()}</p>\n    <p><strong>Overall Status:</strong> <span class=\"${getStatusClass(originalData.overallStatus)}\">${originalData.overallStatus}</span></p>\n  </div>\n\n  <div class=\"summary\">\n    <h3>🤖 AI Analysis Summary</h3>\n    <div style=\"white-space: pre-line; font-size: 14px;\">${aiSummary}</div>\n  </div>\n\n  <div class=\"metrics\">\n    <h3>📊 System Overview</h3>\n    <div class=\"stats-grid\">\n      <div class=\"stat-box\">\n        <strong>Services Monitored:</strong> ${overallStats.totalServices || 0}<br>\n        <strong>Services with Data:</strong> ${overallStats.servicesWithData || 0}\n      </div>\n      <div class=\"stat-box\">\n        <strong>Avg Memory Usage:</strong> ${(overallStats.averageMemoryUsage || 0).toFixed(1)}%<br>\n        <strong>Peak Memory Usage:</strong> ${(overallStats.highestMemoryUsage || 0).toFixed(1)}%\n      </div>\n    </div>\n    \n    <p><strong>Total Metrics:</strong> ${originalData.totalMetrics}</p>\n    <p><strong>Time Range:</strong> ${originalData.timeRange.start || 'N/A'} to ${originalData.timeRange.end || 'N/A'}</p>\n  </div>\n\n  ${overallStats.servicesAbove70Percent?.length > 0 ? `\n  <div class=\"service-list critical-services\">\n    <h4>🚨 Critical Services (>70% Memory)</h4>\n    <ul>\n      ${overallStats.servicesAbove70Percent.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.servicesAbove50Percent?.length > 0 ? `\n  <div class=\"service-list warning-services\">\n    <h4>⚠️ Warning Services (50-70% Memory)</h4>\n    <ul>\n      ${overallStats.servicesAbove50Percent.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.stableServices?.length > 0 ? `\n  <div class=\"service-list stable-services\">\n    <h4>✅ Most Stable Services</h4>\n    <ul>\n      ${overallStats.stableServices.slice(0, 5).map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.variableServices?.length > 0 ? `\n  <div class=\"service-list warning-services\">\n    <h4>📈 Variable Services (High Fluctuation)</h4>\n    <ul>\n      ${overallStats.variableServices.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  <div class=\"footer\">\n    <p>This report was automatically generated by n8n workflow automation.</p>\n    <p>For detailed metrics and raw data, please check the n8n execution logs or CloudWatch dashboard.</p>\n    <p><strong>Next scheduled report:</strong> Check your workflow schedule settings</p>\n  </div>\n</body>\n</html>\n`;\n\nreturn {\n  json: {\n    subject: subject,\n    emailBody: emailBody,\n    plainTextSummary: aiSummary,\n    timestamp: new Date().toISOString(),\n    overallStatus: originalData.overallStatus\n  }\n};"}, "id": "392f2a09-da11-4728-886d-6aa4f6d0011d", "name": "Format Email Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [180, 240], "notes": "Formats the AI summary into a professional HTML email"}, {"parameters": {"fromEmail": "zhang<PERSON>@waveo.com", "toEmail": "zhang<PERSON>@waveo.com", "subject": "={{ $json.subject }}", "emailFormat": "html", "message": "={{ $json.emailBody }}", "options": {"allowUnauthorizedCerts": false}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [320, 240], "id": "2dc736f0-94b7-4549-a9d1-2eacecc5f6d1", "name": "Send Email Report", "webhookId": "0833e569-69a3-4c1a-902d-85c20bf5def0", "credentials": {"smtp": {"id": "wPPmT7ROhWhyqTbJ", "name": "SMTP account"}}, "notes": "Sends the formatted report via email"}, {"parameters": {"workflowId": {"__rl": true, "value": "2IyyBQV1nzQdVNAr", "mode": "list", "cachedResultName": "OPS-Minitor-Cloudwatch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-540, 240], "id": "af88e9db-12fc-468d-9638-bc9539286e2b", "name": "Execute Workflow"}, {"parameters": {"functionCode": "// Create an intelligent summary from the CloudWatch data\n// Since AI API is having issues, we'll create a smart analysis\nconst data = $input.item.json;\nconst formattedData = data.formattedData;\nconst overallStats = formattedData.overallStats;\nconst metrics = formattedData.metrics || [];\n\n// Generate intelligent summary\nlet summary = `## CloudWatch ECS Memory Monitoring Analysis\\n\\n`;\n\n// Overall Health Assessment\nif (formattedData.overallStatus === 'Critical') {\n  summary += `🚨 **CRITICAL ALERT**: System requires immediate attention!\\n\\n`;\n} else if (formattedData.overallStatus === 'Warning') {\n  summary += `⚠️ **WARNING**: Some services need monitoring.\\n\\n`;\n} else if (formattedData.overallStatus === 'Unstable') {\n  summary += `📊 **UNSTABLE**: High variability detected in services.\\n\\n`;\n} else {\n  summary += `✅ **HEALTHY**: System operating within normal parameters.\\n\\n`;\n}\n\n// Key Statistics\nsummary += `### Key Metrics:\\n`;\nsummary += `- **Services Monitored**: ${overallStats.totalServices || 0}\\n`;\nsummary += `- **Services with Data**: ${overallStats.servicesWithData || 0}\\n`;\nsummary += `- **Average Memory Usage**: ${(overallStats.averageMemoryUsage || 0).toFixed(1)}%\\n`;\nsummary += `- **Peak Memory Usage**: ${(overallStats.highestMemoryUsage || 0).toFixed(1)}%\\n\\n`;\n\n// Critical Services\nif (overallStats.servicesAbove70Percent && overallStats.servicesAbove70Percent.length > 0) {\n  summary += `### 🚨 Critical Services (>70% Memory):\\n`;\n  overallStats.servicesAbove70Percent.forEach(service => {\n    summary += `- ${service}\\n`;\n  });\n  summary += `\\n**Recommendation**: Immediate investigation required. Consider scaling up or optimizing these services.\\n\\n`;\n}\n\n// Warning Services\nif (overallStats.servicesAbove50Percent && overallStats.servicesAbove50Percent.length > 0) {\n  summary += `### ⚠️ Warning Services (50-70% Memory):\\n`;\n  overallStats.servicesAbove50Percent.forEach(service => {\n    summary += `- ${service}\\n`;\n  });\n  summary += `\\n**Recommendation**: Monitor closely and prepare for potential scaling.\\n\\n`;\n}\n\n// Stability Analysis\nif (overallStats.variableServices && overallStats.variableServices.length > 0) {\n  summary += `### 📈 High Variability Services:\\n`;\n  overallStats.variableServices.forEach(service => {\n    summary += `- ${service}\\n`;\n  });\n  summary += `\\n**Recommendation**: Investigate workload patterns and consider auto-scaling policies.\\n\\n`;\n}\n\n// Stable Services (positive note)\nif (overallStats.stableServices && overallStats.stableServices.length > 0) {\n  summary += `### ✅ Most Stable Services:\\n`;\n  overallStats.stableServices.slice(0, 3).forEach(service => {\n    summary += `- ${service}\\n`;\n  });\n  summary += `\\n`;\n}\n\n// Top Memory Consumers\nconst topServices = metrics\n  .filter(m => m.statusCode === 'Complete')\n  .sort((a, b) => b.statistics.current - a.statistics.current)\n  .slice(0, 5);\n\nif (topServices.length > 0) {\n  summary += `### 📊 Top Memory Consumers:\\n`;\n  topServices.forEach(service => {\n    const serviceName = service.label.split('-Service-')[0] || service.label;\n    summary += `- ${serviceName}: ${service.statistics.current.toFixed(1)}% (avg: ${service.statistics.average.toFixed(1)}%)\\n`;\n  });\n  summary += `\\n`;\n}\n\n// Action Items\nsummary += `### 🎯 Recommended Actions:\\n`;\nif (overallStats.servicesAbove70Percent && overallStats.servicesAbove70Percent.length > 0) {\n  summary += `1. **Immediate**: Scale up or optimize critical services\\n`;\n}\nif (overallStats.servicesAbove50Percent && overallStats.servicesAbove50Percent.length > 0) {\n  summary += `2. **Short-term**: Monitor warning services closely\\n`;\n}\nif (overallStats.variableServices && overallStats.variableServices.length > 0) {\n  summary += `3. **Medium-term**: Implement auto-scaling for variable services\\n`;\n}\nsummary += `4. **Ongoing**: Continue regular monitoring and optimization\\n\\n`;\n\n// Conclusion\nif (formattedData.overallStatus === 'Critical') {\n  summary += `**Conclusion**: Immediate action required to prevent service degradation.`;\n} else if (formattedData.overallStatus === 'Warning') {\n  summary += `**Conclusion**: System stable but requires attention to prevent issues.`;\n} else {\n  summary += `**Conclusion**: System performing well with normal operational parameters.`;\n}\n\nreturn {\n  json: {\n    text: summary,\n    candidates: [{\n      content: {\n        parts: [{\n          text: summary\n        }]\n      }\n    }]\n  }\n};"}, "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [-180, 240], "id": "0c103ec7-5320-4337-95aa-2338f77eaad7", "name": "Smart Analysis Generator", "notes": "Creates intelligent analysis from CloudWatch data without external AI API"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Format Email Content": {"main": [[{"node": "Send Email Report", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Format Data for AI", "type": "main", "index": 0}]]}, "Format Data for AI": {"main": [[{"node": "Smart Analysis Generator", "type": "main", "index": 0}]]}, "Smart Analysis Generator": {"main": [[{"node": "Format Email Content", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8a3f9c59-3cf6-48dd-85a4-a255e7b33632", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "yVUm5qdyP4f3EuOB", "tags": []}
{
  "name": "OPS-Monitor",
  "nodes": [
    {
      "parameters": {},
      "type": "n8n-nodes-base.manualTrigger",
      "typeVersion": 1,
      "position": [
        -160,
        400
      ],
      "id": "ec2255c9-4620-4406-af41-a5aa10c5eece",
      "name": "When clicking 'Execute workflow'"
    },
    {
      "parameters": {
        "functionCode": "// Extract and format the CloudWatch data for AI processing\n// The data comes directly from the Execute Workflow node\nconst dashboardData = $input.item.json;\n\n// Extract basic information\nconst dashboardName = dashboardData.dashboardName || 'Unknown Dashboard';\nconst retrievedAt = dashboardData.retrievedAt || new Date().toISOString();\nconst timeRange = dashboardData.timeRange || {};\nconst totalMetrics = dashboardData.totalMetrics || 0;\nconst metrics = dashboardData.metrics || [];\nconst summary = dashboardData.summary || {};\nconst messages = dashboardData.messages || [];\n\n// Calculate overall statistics from all metrics\nlet overallStats = {\n  totalServices: metrics.length,\n  servicesWithData: metrics.filter(m => m.statusCode === 'Complete').length,\n  averageMemoryUsage: 0,\n  highestMemoryUsage: 0,\n  lowestMemoryUsage: 100,\n  servicesAbove50Percent: [],\n  servicesAbove70Percent: [],\n  stableServices: [],\n  variableServices: []\n};\n\n// Analyze each metric\nmetrics.forEach(metric => {\n  if (metric.statistics && metric.statusCode === 'Complete') {\n    const current = metric.statistics.current;\n    const average = metric.statistics.average;\n    const max = metric.statistics.maximum;\n    const min = metric.statistics.minimum;\n    \n    // Update overall stats\n    overallStats.averageMemoryUsage += current;\n    if (current > overallStats.highestMemoryUsage) {\n      overallStats.highestMemoryUsage = current;\n    }\n    if (current < overallStats.lowestMemoryUsage) {\n      overallStats.lowestMemoryUsage = current;\n    }\n    \n    // Categorize services by memory usage\n    const serviceName = metric.label.split('-Service-')[0] || metric.label;\n    if (current > 70) {\n      overallStats.servicesAbove70Percent.push(`${serviceName} (${current.toFixed(1)}%)`);\n    } else if (current > 50) {\n      overallStats.servicesAbove50Percent.push(`${serviceName} (${current.toFixed(1)}%)`);\n    }\n    \n    // Check stability (difference between max and min)\n    const variation = max - min;\n    if (variation < 1) {\n      overallStats.stableServices.push(`${serviceName} (±${variation.toFixed(2)}%)`);\n    } else if (variation > 5) {\n      overallStats.variableServices.push(`${serviceName} (±${variation.toFixed(2)}%)`);\n    }\n  }\n});\n\n// Calculate average\nif (overallStats.servicesWithData > 0) {\n  overallStats.averageMemoryUsage = overallStats.averageMemoryUsage / overallStats.servicesWithData;\n}\n\n// Determine overall health status\nlet overallStatus = 'Healthy';\nif (overallStats.servicesAbove70Percent.length > 0) {\n  overallStatus = 'Critical';\n} else if (overallStats.servicesAbove50Percent.length > 3) {\n  overallStatus = 'Warning';\n} else if (overallStats.variableServices.length > 2) {\n  overallStatus = 'Unstable';\n}\n\n// Create detailed text summary for AI processing\nconst textSummary = `\nCloudWatch Dashboard Monitoring Report\n=====================================\n\nDashboard: ${dashboardName}\nRetrieved At: ${retrievedAt}\nTime Range: ${timeRange.start || 'N/A'} to ${timeRange.end || 'N/A'}\nTotal Metrics Collected: ${totalMetrics}\nTotal Data Points: ${summary.totalDataPoints || 0}\nOverall Status: ${overallStatus}\n\nOVERALL STATISTICS:\n- Total Services Monitored: ${overallStats.totalServices}\n- Services with Complete Data: ${overallStats.servicesWithData}\n- Average Memory Usage: ${overallStats.averageMemoryUsage.toFixed(2)}%\n- Highest Memory Usage: ${overallStats.highestMemoryUsage.toFixed(2)}%\n- Lowest Memory Usage: ${overallStats.lowestMemoryUsage.toFixed(2)}%\n\nSERVICES REQUIRING ATTENTION:\n${overallStats.servicesAbove70Percent.length > 0 ? \n  'Critical (>70% Memory):\\n' + overallStats.servicesAbove70Percent.map(s => '  - ' + s).join('\\n') : \n  'No services above 70% memory usage'}\n\n${overallStats.servicesAbove50Percent.length > 0 ? \n  'Warning (50-70% Memory):\\n' + overallStats.servicesAbove50Percent.map(s => '  - ' + s).join('\\n') : \n  'No services in warning range'}\n\nSERVICE STABILITY:\n${overallStats.stableServices.length > 0 ? \n  'Stable Services (low variation):\\n' + overallStats.stableServices.map(s => '  - ' + s).join('\\n') : \n  'No highly stable services detected'}\n\n${overallStats.variableServices.length > 0 ? \n  'Variable Services (high variation):\\n' + overallStats.variableServices.map(s => '  - ' + s).join('\\n') : \n  'No highly variable services detected'}\n\nTOP 5 HIGHEST MEMORY USAGE SERVICES:\n${metrics\n  .filter(m => m.statusCode === 'Complete')\n  .sort((a, b) => b.statistics.current - a.statistics.current)\n  .slice(0, 5)\n  .map(m => `  - ${m.label.split('-Service-')[0]}: ${m.statistics.current.toFixed(2)}% (avg: ${m.statistics.average.toFixed(2)}%)`)\n  .join('\\n')}\n\nMESSAGES/ALERTS:\n${messages.length > 0 ? messages.join('\\n') : 'No alerts or messages'}\n\nRAW METRIC DETAILS:\n${JSON.stringify(metrics.slice(0, 3), null, 2)}...\n(Showing first 3 metrics for reference)\n`;\n\n// Format the data for further processing\nconst formattedData = {\n  dashboardName: dashboardName,\n  retrievedAt: retrievedAt,\n  timeRange: timeRange,\n  totalMetrics: totalMetrics,\n  overallStats: overallStats,\n  overallStatus: overallStatus,\n  metrics: metrics,\n  summary: summary,\n  messages: messages\n};\n\nreturn {\n  json: {\n    formattedData: formattedData,\n    textSummary: textSummary,\n    rawDashboardData: dashboardData\n  }\n};"
      },
      "id": "1c826cca-37cb-4482-8702-cb1241f1d7ca",
      "name": "Format Data for AI",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        260,
        400
      ],
      "notes": "Extracts and formats CloudWatch data for AI summarization"
    },
    {
      "parameters": {
        "resource": "text",
        "operation": "complete",
        "model": "gpt-4",
        "prompt": "You are an expert DevOps analyst specializing in AWS CloudWatch monitoring and memory usage analysis. Your task is to analyze CloudWatch dashboard data for a CRM production environment and provide a concise, actionable summary for operations teams.\n\nFocus on:\n1. Overall system health status and trends\n2. Memory usage patterns and concerning metrics\n3. Service stability and performance variations\n4. Immediate action items and recommendations\n5. Priority alerts or issues requiring attention\n\nProvide a professional, clear summary under 400 words with specific recommendations.\n\nCloudWatch Dashboard Data to Analyze:\n{{ $json.textSummary }}\n\nOperational Summary:",
        "options": {
          "temperature": 0.3,
          "maxTokens": 600,
          "topP": 1,
          "frequencyPenalty": 0,
          "presencePenalty": 0
        },
        "requestOptions": {}
      },
      "type": "n8n-nodes-base.openAi",
      "typeVersion": 1,
      "position": [
        460,
        400
      ],
      "id": "b086198c-4c14-457e-b076-a4a73d9e9372",
      "name": "AI Summarize Data",
      "credentials": {
        "openAiApi": {
          "id": "Fr2SxYeJcPf9XceP",
          "name": "OpenAi account"
        }
      },
      "notes": "Uses GPT-4 to create an intelligent summary of the CloudWatch data"
    },
    {
      "parameters": {
        "functionCode": "// Format the AI summary for email\n// Handle both 'complete' and 'message' operation response formats\nconst aiSummary = $input.item.json.text || $input.item.json.message?.content || $input.item.json.choices?.[0]?.text || 'No summary available';"\nconst originalData = $('Format Data for AI').item.json.formattedData;\nconst overallStats = originalData.overallStats || {};\n\n// Create email subject with status indicator\nconst currentDate = new Date().toLocaleDateString();\nconst currentTime = new Date().toLocaleTimeString();\nconst statusEmoji = originalData.overallStatus === 'Critical' ? '🚨' : \n                   originalData.overallStatus === 'Warning' ? '⚠️' : \n                   originalData.overallStatus === 'Unstable' ? '📊' : '✅';\nconst subject = `${statusEmoji} CRM Monitoring Report - ${originalData.dashboardName} - ${currentDate}`;\n\n// Determine status color class\nconst getStatusClass = (status) => {\n  switch(status?.toLowerCase()) {\n    case 'critical': return 'status-error';\n    case 'warning': return 'status-warning';\n    case 'unstable': return 'status-warning';\n    default: return 'status-ok';\n  }\n};\n\n// Create email body with enhanced HTML formatting\nconst emailBody = `\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 0 auto; }\n    .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }\n    .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .metrics { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }\n    .stat-box { background: white; padding: 10px; border-radius: 3px; border-left: 4px solid #007bff; }\n    .footer { font-size: 12px; color: #666; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }\n    .status-ok { color: #28a745; font-weight: bold; }\n    .status-warning { color: #ffc107; font-weight: bold; }\n    .status-error { color: #dc3545; font-weight: bold; }\n    .service-list { background: #f8f9fa; padding: 10px; border-radius: 3px; margin: 10px 0; }\n    .critical-services { border-left: 4px solid #dc3545; }\n    .warning-services { border-left: 4px solid #ffc107; }\n    .stable-services { border-left: 4px solid #28a745; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h2>${statusEmoji} CloudWatch CRM Production Monitoring Report</h2>\n    <p><strong>Dashboard:</strong> ${originalData.dashboardName}</p>\n    <p><strong>Generated:</strong> ${currentDate} at ${currentTime}</p>\n    <p><strong>Retrieved At:</strong> ${new Date(originalData.retrievedAt).toLocaleString()}</p>\n    <p><strong>Overall Status:</strong> <span class=\"${getStatusClass(originalData.overallStatus)}\">${originalData.overallStatus}</span></p>\n  </div>\n\n  <div class=\"summary\">\n    <h3>🤖 AI Analysis Summary</h3>\n    <div style=\"white-space: pre-line; font-size: 14px;\">${aiSummary}</div>\n  </div>\n\n  <div class=\"metrics\">\n    <h3>📊 System Overview</h3>\n    <div class=\"stats-grid\">\n      <div class=\"stat-box\">\n        <strong>Services Monitored:</strong> ${overallStats.totalServices || 0}<br>\n        <strong>Services with Data:</strong> ${overallStats.servicesWithData || 0}\n      </div>\n      <div class=\"stat-box\">\n        <strong>Avg Memory Usage:</strong> ${(overallStats.averageMemoryUsage || 0).toFixed(1)}%<br>\n        <strong>Peak Memory Usage:</strong> ${(overallStats.highestMemoryUsage || 0).toFixed(1)}%\n      </div>\n    </div>\n    \n    <p><strong>Total Metrics:</strong> ${originalData.totalMetrics}</p>\n    <p><strong>Time Range:</strong> ${originalData.timeRange.start || 'N/A'} to ${originalData.timeRange.end || 'N/A'}</p>\n  </div>\n\n  ${overallStats.servicesAbove70Percent?.length > 0 ? `\n  <div class=\"service-list critical-services\">\n    <h4>🚨 Critical Services (>70% Memory)</h4>\n    <ul>\n      ${overallStats.servicesAbove70Percent.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.servicesAbove50Percent?.length > 0 ? `\n  <div class=\"service-list warning-services\">\n    <h4>⚠️ Warning Services (50-70% Memory)</h4>\n    <ul>\n      ${overallStats.servicesAbove50Percent.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.stableServices?.length > 0 ? `\n  <div class=\"service-list stable-services\">\n    <h4>✅ Most Stable Services</h4>\n    <ul>\n      ${overallStats.stableServices.slice(0, 5).map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  ${overallStats.variableServices?.length > 0 ? `\n  <div class=\"service-list warning-services\">\n    <h4>📈 Variable Services (High Fluctuation)</h4>\n    <ul>\n      ${overallStats.variableServices.map(service => `<li>${service}</li>`).join('')}\n    </ul>\n  </div>\n  ` : ''}\n\n  <div class=\"footer\">\n    <p>This report was automatically generated by n8n workflow automation.</p>\n    <p>For detailed metrics and raw data, please check the n8n execution logs or CloudWatch dashboard.</p>\n    <p><strong>Next scheduled report:</strong> Check your workflow schedule settings</p>\n  </div>\n</body>\n</html>\n`;\n\nreturn {\n  json: {\n    subject: subject,\n    emailBody: emailBody,\n    plainTextSummary: aiSummary,\n    timestamp: new Date().toISOString(),\n    overallStatus: originalData.overallStatus\n  }\n};"
      },
      "id": "bf3fcd54-716b-44f9-a10d-fc22a638fcb0",
      "name": "Format Email Content",
      "type": "n8n-nodes-base.function",
      "typeVersion": 1,
      "position": [
        660,
        400
      ],
      "notes": "Formats the AI summary into a professional HTML email"
    },
    {
      "parameters": {
        "fromEmail": "<EMAIL>",
        "toEmail": "<EMAIL>",
        "subject": "={{ $json.subject }}",
        "emailFormat": "html",
        "message": "={{ $json.emailBody }}",
        "options": {
          "allowUnauthorizedCerts": false
        }
      },
      "type": "n8n-nodes-base.emailSend",
      "typeVersion": 2,
      "position": [
        860,
        400
      ],
      "id": "e589ed86-267a-417b-8fa8-38090c034688",
      "name": "Send Email Report",
      "webhookId": "0833e569-69a3-4c1a-902d-85c20bf5def0",
      "credentials": {
        "smtp": {
          "id": "wPPmT7ROhWhyqTbJ",
          "name": "SMTP account"
        }
      },
      "notes": "Sends the formatted report via email"
    },
    {
      "parameters": {
        "workflowId": {
          "__rl": true,
          "value": "2IyyBQV1nzQdVNAr",
          "mode": "list",
          "cachedResultName": "OPS-Minitor-Cloudwatch"
        },
        "workflowInputs": {
          "mappingMode": "defineBelow",
          "value": {},
          "matchingColumns": [],
          "schema": [],
          "attemptToConvertTypes": false,
          "convertFieldsToString": true
        },
        "options": {
          "waitForSubWorkflow": true
        }
      },
      "type": "n8n-nodes-base.executeWorkflow",
      "typeVersion": 1.2,
      "position": [
        60,
        400
      ],
      "id": "47e18760-603a-4720-9380-db6f9661ecc3",
      "name": "Execute Workflow"
    }
  ],
  "pinData": {},
  "connections": {
    "When clicking 'Execute workflow'": {
      "main": [
        [
          {
            "node": "Execute Workflow",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Data for AI": {
      "main": [
        [
          {
            "node": "AI Summarize Data",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "AI Summarize Data": {
      "main": [
        [
          {
            "node": "Format Email Content",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Format Email Content": {
      "main": [
        [
          {
            "node": "Send Email Report",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Execute Workflow": {
      "main": [
        [
          {
            "node": "Format Data for AI",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "active": false,
  "settings": {
    "executionOrder": "v1"
  },
  "versionId": "5139ab1e-1637-4ce5-9426-e7629ab72f17",
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"
  },
  "id": "DSWQRFScWJfsYj9m",
  "tags": []
}
{"name": "OPS-Monitor", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-160, 400], "id": "ec2255c9-4620-4406-af41-a5aa10c5eece", "name": "When clicking 'Execute workflow'"}, {"parameters": {"functionCode": "// Extract and format the CloudWatch data for AI processing\nconst workflowData = $input.item.json;\n\n// Get the execution data from the workflow response\nconst executionData = workflowData.data || {};\nconst lastNodeData = executionData.resultData?.lastNodeExecuted || {};\n\n// Extract the formatted dashboard data\nlet dashboardData = {};\nif (lastNodeData && lastNodeData.data && lastNodeData.data.main && lastNodeData.data.main[0]) {\n  dashboardData = lastNodeData.data.main[0][0]?.json || {};\n}\n\n// Format the data for AI summarization\nconst formattedData = {\n  executionId: workflowData.executionId,\n  executionTime: workflowData.startedAt,\n  dashboardName: dashboardData.dashboardName || 'Unknown Dashboard',\n  timeRange: dashboardData.timeRange || {},\n  metricSummary: dashboardData.metricSummary || {},\n  metricData: dashboardData.metricData || [],\n  totalMetrics: dashboardData.totalMetrics || 0,\n  alertsTriggered: dashboardData.alertsTriggered || [],\n  overallStatus: dashboardData.overallStatus || 'Unknown'\n};\n\n// Create a text summary for AI processing\nconst textSummary = `\nCloudWatch Dashboard Monitoring Report\n=====================================\n\nDashboard: ${formattedData.dashboardName}\nExecution Time: ${formattedData.executionTime}\nTime Range: ${formattedData.timeRange.start} to ${formattedData.timeRange.end}\nTotal Metrics Collected: ${formattedData.totalMetrics}\nOverall Status: ${formattedData.overallStatus}\n\nMetric Summary:\n${JSON.stringify(formattedData.metricSummary, null, 2)}\n\nAlerts Triggered: ${formattedData.alertsTriggered.length > 0 ? formattedData.alertsTriggered.join(', ') : 'None'}\n\nDetailed Metric Data:\n${JSON.stringify(formattedData.metricData, null, 2)}\n`;\n\nreturn {\n  json: {\n    formattedData: formattedData,\n    textSummary: textSummary,\n    rawWorkflowData: workflowData\n  }\n};"}, "id": "1c826cca-37cb-4482-8702-cb1241f1d7ca", "name": "Format Data for AI", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [260, 400], "notes": "Extracts and formats CloudWatch data for AI summarization"}, {"parameters": {"operation": "message", "requestOptions": {}}, "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [460, 400], "id": "b086198c-4c14-457e-b076-a4a73d9e9372", "name": "AI Summarize Data", "credentials": {"openAiApi": {"id": "Fr2SxYeJcPf9XceP", "name": "OpenAi account"}}, "notes": "Uses GPT-4 to create an intelligent summary of the CloudWatch data"}, {"parameters": {"functionCode": "// Format the AI summary for email\nconst aiSummary = $input.item.json.message?.content || 'No summary available';\nconst originalData = $('Format Data for AI').item.json.formattedData;\n\n// Create email subject\nconst currentDate = new Date().toLocaleDateString();\nconst currentTime = new Date().toLocaleTimeString();\nconst subject = `CloudWatch Monitoring Report - ${originalData.dashboardName} - ${currentDate}`;\n\n// Create email body with HTML formatting\nconst emailBody = `\n<html>\n<head>\n  <style>\n    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n    .header { background-color: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }\n    .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .metrics { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0; }\n    .footer { font-size: 12px; color: #666; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }\n    .status-ok { color: #28a745; font-weight: bold; }\n    .status-warning { color: #ffc107; font-weight: bold; }\n    .status-error { color: #dc3545; font-weight: bold; }\n  </style>\n</head>\n<body>\n  <div class=\"header\">\n    <h2>🔍 CloudWatch Monitoring Report</h2>\n    <p><strong>Dashboard:</strong> ${originalData.dashboardName}</p>\n    <p><strong>Generated:</strong> ${currentDate} at ${currentTime}</p>\n    <p><strong>Execution ID:</strong> ${originalData.executionId}</p>\n  </div>\n\n  <div class=\"summary\">\n    <h3>📊 AI Analysis Summary</h3>\n    <div style=\"white-space: pre-line;\">${aiSummary}</div>\n  </div>\n\n  <div class=\"metrics\">\n    <h3>📈 Quick Stats</h3>\n    <ul>\n      <li><strong>Total Metrics:</strong> ${originalData.totalMetrics}</li>\n      <li><strong>Time Range:</strong> ${originalData.timeRange.start || 'N/A'} to ${originalData.timeRange.end || 'N/A'}</li>\n      <li><strong>Overall Status:</strong> <span class=\"status-${originalData.overallStatus?.toLowerCase()}\">${originalData.overallStatus}</span></li>\n      <li><strong>Alerts:</strong> ${originalData.alertsTriggered?.length > 0 ? originalData.alertsTriggered.join(', ') : 'None'}</li>\n    </ul>\n  </div>\n\n  <div class=\"footer\">\n    <p>This report was automatically generated by n8n workflow automation.</p>\n    <p>For detailed metrics and raw data, please check the n8n execution logs.</p>\n  </div>\n</body>\n</html>\n`;\n\nreturn {\n  json: {\n    subject: subject,\n    emailBody: emailBody,\n    plainTextSummary: aiSummary,\n    timestamp: new Date().toISOString()\n  }\n};"}, "id": "bf3fcd54-716b-44f9-a10d-fc22a638fcb0", "name": "Format Email Content", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [660, 400], "notes": "Formats the AI summary into a professional HTML email"}, {"parameters": {"fromEmail": "zhang<PERSON>@waveo.com", "toEmail": "zhang<PERSON>@waveo.com", "subject": "={{ $json.subject }}", "emailFormat": "html", "options": {"allowUnauthorizedCerts": false}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [860, 400], "id": "e589ed86-267a-417b-8fa8-38090c034688", "name": "Send Email Report", "webhookId": "0833e569-69a3-4c1a-902d-85c20bf5def0", "credentials": {"smtp": {"id": "wPPmT7ROhWhyqTbJ", "name": "SMTP account"}}, "notes": "Sends the formatted report via email"}, {"parameters": {"workflowId": {"__rl": true, "value": "2IyyBQV1nzQdVNAr", "mode": "list", "cachedResultName": "OPS-Minitor-Cloudwatch"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [60, 400], "id": "47e18760-603a-4720-9380-db6f9661ecc3", "name": "Execute Workflow"}], "pinData": {}, "connections": {"When clicking 'Execute workflow'": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Format Data for AI": {"main": [[{"node": "AI Summarize Data", "type": "main", "index": 0}]]}, "AI Summarize Data": {"main": [[{"node": "Format Email Content", "type": "main", "index": 0}]]}, "Format Email Content": {"main": [[{"node": "Send Email Report", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Format Data for AI", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5139ab1e-1637-4ce5-9426-e7629ab72f17", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "DSWQRFScWJfsYj9m", "tags": []}
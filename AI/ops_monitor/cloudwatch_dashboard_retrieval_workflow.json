{"name": "CloudWatch Dashboard Retriever", "nodes": [{"parameters": {}, "id": "start-node", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [100, 300]}, {"parameters": {"service": "CloudWatch", "region": "ap-southeast-1", "operation": "GetDashboard", "parameters": {"DashboardName": "MemoryMonitor-CRM-production"}}, "id": "aws-sdk-node", "name": "AWS SDK - Get Dashboard", "type": "n8n-nodes-aws-sdk-v3.awsSdk", "typeVersion": 1, "position": [300, 300], "credentials": {"aws": {"id": "__AWS_CREDENTIAL_ID__", "name": "AWS Account"}}, "notes": "Retrieves the CloudWatch dashboard 'MemoryMonitor-CRM-production' from ap-southeast-1 region"}, {"parameters": {"functionCode": "// Parse the dashboard body JSON string into an object\nconst dashboardData = JSON.parse($input.item.json.DashboardBody);\n\n// Format the output for easier processing\nreturn {\n  json: {\n    dashboardName: $input.item.json.DashboardName,\n    dashboardArn: $input.item.json.DashboardArn,\n    dashboardBody: dashboardData,\n    // Extract widgets for easier access\n    widgets: dashboardData.widgets || [],\n    // Add timestamp for reference\n    retrievedAt: new Date().toISOString()\n  }\n};"}, "id": "format-output-node", "name": "Format Dashboard Data", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [500, 300], "notes": "Parses the dashboard JSON body and formats it for easier use in subsequent nodes"}], "connections": {"Start": {"main": [[{"node": "AWS SDK - Get Dashboard", "type": "main", "index": 0}]]}, "AWS SDK - Get Dashboard": {"main": [[{"node": "Format Dashboard Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "tags": ["aws", "cloudwatch", "dashboard"]}
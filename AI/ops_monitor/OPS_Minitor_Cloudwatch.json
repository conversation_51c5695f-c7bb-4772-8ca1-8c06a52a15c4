{"name": "OPS-Minitor-Cloudwatch", "nodes": [{"parameters": {"functionCode": "// Process memory metric statistics data\nconst metricData = $input.item.json;\n\n// Calculate statistics from datapoints\nconst datapoints = metricData.Datapoints || [];\nconst sortedDatapoints = datapoints.sort((a, b) => new Date(a.Timestamp) - new Date(b.Timestamp));\n\n// Calculate summary statistics\nconst averageValues = datapoints.map(dp => dp.Average).filter(val => val !== undefined);\nconst maxValues = datapoints.map(dp => dp.Maximum).filter(val => val !== undefined);\nconst minValues = datapoints.map(dp => dp.Minimum).filter(val => val !== undefined);\n\nconst summary = {\n  totalDatapoints: datapoints.length,\n  timeRange: {\n    start: sortedDatapoints.length > 0 ? sortedDatapoints[0].Timestamp : null,\n    end: sortedDatapoints.length > 0 ? sortedDatapoints[sortedDatapoints.length - 1].Timestamp : null\n  },\n  memoryUtilization: {\n    current: sortedDatapoints.length > 0 ? sortedDatapoints[sortedDatapoints.length - 1].Average : null,\n    average: averageValues.length > 0 ? averageValues.reduce((sum, val) => sum + val, 0) / averageValues.length : null,\n    peak: maxValues.length > 0 ? Math.max(...maxValues) : null,\n    minimum: minValues.length > 0 ? Math.min(...minValues) : null\n  }\n};\n\n// Format the output for easier processing\nreturn {\n  json: {\n    metricName: metricData.Label || 'MemoryUtilization',\n    namespace: 'AWS/EC2',\n    datapoints: sortedDatapoints,\n    summary: summary,\n    retrievedAt: new Date().toISOString(),\n    source: 'MemoryMonitor-CRM-production Dashboard'\n  }\n};"}, "id": "13a54d2e-1b3d-4ae1-ad34-121b1ca1c6a1", "name": "Format Memory Metrics", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [820, -220], "notes": "Processes memory metric statistics and calculates summary data for monitoring"}, {"parameters": {"region": "ap-southeast-1", "service": "CloudWatch", "operation": "GetMetricStatistics", "requestHasInput": true, "requestInput": "{\n  \"Namespace\": \"AWS/EC2\",\n  \"MetricName\": \"MemoryUtilization\",\n  \"StartTime\": \"{{ $now.minus({hours: 1}).toISO() }}\",\n  \"EndTime\": \"{{ $now.toISO() }}\",\n  \"Period\": 300,\n  \"Statistics\": [\"Average\", \"Maximum\", \"Minimum\"]\n}", "version": "2010-08-01"}, "type": "n8n-nodes-aws-sdk-v3.AWSSDKWrapper", "typeVersion": 1, "position": [640, -220], "id": "f4787283-4ec8-4a1e-84e8-9a4b72a8aa08", "name": "Get Memory Metrics", "credentials": {"awsSdkWrapperCredentialsApi": {"id": "XCHImWEP1FWyMVeg", "name": "AWS SDK Wrapper Credentials account"}}}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [440, -220], "id": "641715bf-c062-40f3-a33f-0022b261d7dc", "name": "When clicking ‘Execute workflow’"}], "pinData": {}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "AWS Service Request", "type": "main", "index": 0}]]}, "AWS Service Request": {"main": [[{"node": "Format Dashboard Data", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "72713bae-0c77-44f9-a5a0-79f0117fdaff", "meta": {"templateCredsSetupCompleted": true, "instanceId": "05e02a9421dabd20d1f06b1b8200138a27e3ea0dda7fbe144fe4915b736d008b"}, "id": "2IyyBQV1nzQdVNAr", "tags": []}